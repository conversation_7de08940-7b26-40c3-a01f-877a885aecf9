{{-- WIDDX AI Advanced Input Area Component --}}
<footer class="widdx-input-area">
    <div class="widdx-container">
        {{-- File Upload Preview --}}
        <div id="file-preview-area" class="widdx-file-preview hidden">
            <div class="flex items-center space-x-2 p-3 bg-widdx-bg-elevated rounded-lg mb-3">
                <div class="flex-1">
                    <div id="file-preview-list" class="flex flex-wrap gap-2">
                        {{-- File previews will be added here dynamically --}}
                    </div>
                </div>
                <button id="clear-files" class="widdx-btn widdx-btn-ghost p-1" data-tooltip="Clear Files">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        {{-- Main Input Container --}}
        <div class="widdx-input-container">
            {{-- Input Actions Bar --}}
            <div class="widdx-input-actions">
                {{-- File Upload --}}
                <button id="file-upload-btn" class="widdx-input-action" data-tooltip="Upload File">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                    </svg>
                </button>

                {{-- Image Upload --}}
                <button id="image-upload-btn" class="widdx-input-action" data-tooltip="Upload Image">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </button>

                {{-- Voice Input --}}
                <button id="voice-input-btn" class="widdx-input-action" data-tooltip="Voice Input">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                </button>

                {{-- Camera Capture --}}
                <button id="camera-btn" class="widdx-input-action" data-tooltip="Capture Image">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </button>
            </div>

            {{-- Text Input Area --}}
            <div class="widdx-input-wrapper">
                <div class="widdx-input-field">
                    <textarea
                        id="message-input"
                        placeholder="Type your message here... (Ctrl+Enter to send)"
                        class="widdx-textarea"
                        rows="1"
                        maxlength="10000"
                    ></textarea>

                    {{-- Input Suggestions --}}
                    <div id="input-suggestions" class="widdx-input-suggestions hidden">
                        <div class="widdx-suggestions-list">
                            {{-- Suggestions will be populated dynamically --}}
                        </div>
                    </div>
                </div>

                {{-- Character Counter --}}
                <div class="widdx-char-counter">
                    <span id="char-count">0</span><span class="text-widdx-text-muted">/10000</span>
                </div>
            </div>

            {{-- Send Button --}}
            <div class="widdx-send-area">
                <button
                    id="send-button"
                    class="widdx-send-btn"
                    disabled
                    data-tooltip="Send (Ctrl+Enter)"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>

        {{-- Quick Commands --}}
        <div id="quick-commands" class="widdx-quick-commands hidden">
            <div class="flex items-center space-x-2 text-xs text-widdx-text-tertiary">
                <span>Quick commands:</span>
                <button class="widdx-quick-cmd" data-command="/search">Search</button>
                <button class="widdx-quick-cmd" data-command="/image">Generate Image</button>
                <button class="widdx-quick-cmd" data-command="/think">Think Mode</button>
                <button class="widdx-quick-cmd" data-command="/help">Help</button>
            </div>
        </div>

        {{-- Voice Recording Indicator --}}
        <div id="voice-recording" class="widdx-voice-recording hidden">
            <div class="flex items-center justify-center space-x-3 p-4 bg-widdx-bg-elevated rounded-lg">
                <div class="widdx-recording-indicator">
                    <div class="widdx-pulse-dot"></div>
                </div>
                <span class="text-widdx-text-primary">Recording...</span>
                <button id="stop-recording" class="widdx-btn widdx-btn-secondary">
                    Stop
                </button>
            </div>
        </div>
    </div>

    {{-- Hidden File Inputs --}}
    <input type="file" id="file-input" class="hidden" multiple accept="*/*">
    <input type="file" id="image-input" class="hidden" multiple accept="image/*">
</footer>

<style>
.widdx-input-area {
    @apply bg-widdx-bg-secondary border-t border-widdx-border-primary p-4 flex-shrink-0;
}

.widdx-file-preview {
    @apply animate-slide-down;
}

.widdx-file-preview-item {
    @apply flex items-center space-x-2 bg-widdx-bg-primary rounded-lg p-2 border border-widdx-border-primary;
}

.widdx-file-preview-item .file-icon {
    @apply w-8 h-8 rounded bg-widdx-primary flex items-center justify-center text-white text-xs font-bold;
}

.widdx-file-preview-item .file-info {
    @apply flex-1 min-w-0;
}

.widdx-file-preview-item .file-name {
    @apply text-sm text-widdx-text-primary truncate;
}

.widdx-file-preview-item .file-size {
    @apply text-xs text-widdx-text-tertiary;
}

.widdx-input-container {
    @apply flex items-end space-x-3 bg-widdx-bg-elevated border border-widdx-border-primary rounded-2xl p-3;
}

.widdx-input-actions {
    @apply flex items-center space-x-1;
}

.widdx-input-action {
    @apply p-2 text-widdx-text-secondary hover:text-widdx-text-primary hover:bg-widdx-bg-hover rounded-lg transition-all duration-200;
}

.widdx-input-action:hover {
    @apply transform scale-110;
}

.widdx-input-action.active {
    @apply text-widdx-primary bg-widdx-bg-hover;
}

.widdx-input-wrapper {
    @apply flex-1 relative;
}

.widdx-input-field {
    @apply relative;
}

.widdx-textarea {
    @apply w-full bg-transparent border-none outline-none text-widdx-text-primary placeholder-widdx-text-muted resize-none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

.widdx-char-counter {
    @apply absolute bottom-1 right-2 text-xs text-widdx-text-muted;
}

.widdx-char-counter.warning {
    @apply text-widdx-status-warning;
}

.widdx-char-counter.error {
    @apply text-widdx-status-error;
}

.widdx-send-area {
    @apply flex items-center;
}

.widdx-send-btn {
    @apply w-10 h-10 bg-widdx-primary hover:bg-widdx-primary-hover text-white rounded-full flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.widdx-send-btn:not(:disabled):hover {
    @apply transform scale-110 shadow-widdx-glow;
}

.widdx-send-btn:not(:disabled):active {
    @apply transform scale-95;
}

.widdx-input-suggestions {
    @apply absolute bottom-full left-0 right-0 mb-2 bg-widdx-bg-elevated border border-widdx-border-primary rounded-lg shadow-widdx-lg z-50;
}

.widdx-suggestions-list {
    @apply max-h-40 overflow-y-auto scrollbar-thin;
}

.widdx-suggestion-item {
    @apply px-3 py-2 text-sm text-widdx-text-secondary hover:text-widdx-text-primary hover:bg-widdx-bg-hover cursor-pointer transition-colors;
}

.widdx-suggestion-item.active {
    @apply bg-widdx-bg-hover text-widdx-text-primary;
}

.widdx-quick-commands {
    @apply mt-2 animate-fade-in;
}

.widdx-quick-cmd {
    @apply px-2 py-1 bg-widdx-bg-elevated hover:bg-widdx-bg-hover rounded text-widdx-text-secondary hover:text-widdx-text-primary transition-colors;
}

.widdx-voice-recording {
    @apply animate-slide-up;
}

.widdx-recording-indicator {
    @apply relative w-4 h-4;
}

.widdx-pulse-dot {
    @apply absolute inset-0 bg-widdx-status-error rounded-full animate-ping;
}

.widdx-pulse-dot::after {
    @apply content-[''] absolute inset-1 bg-widdx-status-error rounded-full;
}

/* Auto-resize textarea */
.widdx-textarea:focus {
    @apply outline-none;
}

/* Drag and drop styles */
.widdx-input-container.drag-over {
    @apply border-widdx-primary bg-widdx-bg-hover;
}

.widdx-drag-overlay {
    @apply absolute inset-0 bg-widdx-primary bg-opacity-10 border-2 border-dashed border-widdx-primary rounded-2xl flex items-center justify-center;
}

.widdx-drag-overlay-content {
    @apply text-center text-widdx-primary;
}

/* Responsive */
@media (max-width: 768px) {
    .widdx-input-container {
        @apply p-2 space-x-2;
    }

    .widdx-input-actions {
        @apply space-x-0.5;
    }

    .widdx-input-action {
        @apply p-1.5;
    }

    .widdx-send-btn {
        @apply w-8 h-8;
    }

    .widdx-quick-commands {
        @apply text-xs;
    }
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
